# What's Changed

> 对 v3.5.16 的修订版本

1. 新增：支持接入 Slack
2. 新增：支持接入 Discord
3. 新增：支持接入 KOOK
4. 新增：支持接入 VoceChat
5. 新增：微信客服支持语音的收发
6. 新增：实现 WebUI 的 i18n 模型，WebUI 现已支持 English。
7. 新增：支持接入 GPT SoVITS
8. 优化：支持通过引用 Bot 消息来唤醒 Bot
9.  优化：WebUI 滚动条、侧边栏样式优化
10. 优化：WebUI ChatBox 的样式优化，添加切换夜间模式按钮
11. 优化：WebUI Chat 页面的 SSE 连接优化及一些其他样式优化
12. 优化：钉钉发送图片支持使用 AstrBot 自带的文件服务器
13. 优化：新建服务提供商时，如果没有添加 Key，会弹出警告提示框
14. 修复：会话隔离模式下，WeChatPadPro 会话 ID 为自身 ID
15. 修复：会话隔离模式下，WeChatPadPro 无法回复群聊消息
16. 修复：使用 uvx 启动 AstrBot 时，插件依赖无法正常安装
