1. 改进: WebUI提供商徽标显示
2. 修复：在LLMRequestSubStage中添加对提供商请求处理的调试日志记录
3. 修复: 为嵌入模型提供商添加状态检查
4. 新增: 支持在WebUI上管理会话
5. 新增: 为ProviderMetadata添加provider_type字段并优化提供商可用性测试
6. 改进: WebUI聊天页面Markdown代码块
7. 修复: 讯飞模型工具使用错误
8. 修复: 修复mcp导致的持续占用100% CPU
9. 重构: mcp服务器重载机制
10. 新增: 为WebChat页面添加文件上传按钮
11. 优化: 工具使用页面用户界面
12. 新增: 添加测试GitHub加速地址的组件
13. 新增: 使用会话锁保证分段回复时的消息发送顺序
14. 新增: 实现日志历史记录检索并改进日志流处理
15. 杂务: 修改openai的嵌入模型默认维度为1024
16. 修复：更新axios版本范围
17. chore: remove adapters of WeChat personal account(gewechat)
18. 新增: 为AstrBotConfig中的嵌套对象添加展开状态管理