{"title": "Long-term Memory", "subtitle": "AI assistant's long-term memory management", "memories": {"title": "Memory List", "content": "Memory Content", "importance": "Importance Level", "createTime": "Create Time", "lastAccess": "Last Access", "category": "Category"}, "categories": {"personal": "Personal Information", "preferences": "Preference Settings", "conversations": "Conversation History", "facts": "Factual Information", "skills": "Skill Knowledge"}, "importance": {"high": "High", "medium": "Medium", "low": "Low"}, "actions": {"view": "View Details", "edit": "Edit", "delete": "Delete", "pin": "<PERSON>n", "unpin": "Unpin"}, "filters": {"all": "All", "category": "By Category", "importance": "By Importance", "dateRange": "By Date Range", "title": "Filters", "userIdLabel": "Filter by User ID", "filterButton": "Filter", "resetButton": "Reset Filter", "refreshButton": "Refresh G<PERSON>h"}, "search": {"title": "Search Memory", "userIdLabel": "User ID", "queryLabel": "Enter keywords", "searchButton": "Search", "resultsTitle": "Search Results", "noResults": "No relevant memory content found", "similarity": "Relevance", "noTextContent": "No text content"}, "addMemory": {"title": "Add Memory Data", "textLabel": "Enter text content", "userIdLabel": "User ID", "summarizeLabel": "Need summary", "addButton": "Add Data"}, "nodeDetails": {"title": "Node Details", "id": "ID", "type": "Type", "name": "Name", "userId": "User ID", "timestamp": "Timestamp"}, "graphStats": {"title": "Graph Statistics", "nodeCount": "Node Count", "edgeCount": "Edge Count"}, "factDialog": {"title": "Memory Fact", "id": "ID", "docId": "Document ID", "createdAt": "Created At", "updatedAt": "Updated At", "metadata": "<PERSON><PERSON><PERSON>", "metadataKey": "Key", "metadataValue": "Value", "loading": "Loading...", "close": "Close", "noValue": "None", "unknown": "Unknown"}, "messages": {"searchQueryRequired": "Please enter search keywords", "searchSuccess": "Found {count} relevant memories", "searchNoResults": "No relevant memory content found", "searchError": "Search failed", "addSuccess": "Memory data added successfully!", "addError": "Failed to add memory data", "factDetailsError": "Failed to get memory details", "metadataParseError": "Unable to parse metadata", "relationNoMemoryData": "This relation has no associated memory data"}}